package com.ruoyi.expense.service.impl;

import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.expense.domain.ExpenseAllocationTable;
import com.ruoyi.expense.domain.Expense_wechat;
import com.ruoyi.expense.service.IExpenseAllocationTableNotificationService;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysRoleService;
import com.ruoyi.system.service.ISysUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * 分摊表通知服务实现类
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
@Service
public class ExpenseAllocationTableNotificationServiceImpl implements IExpenseAllocationTableNotificationService {
    
    private static final Logger log = LoggerFactory.getLogger(ExpenseAllocationTableNotificationServiceImpl.class);

    @Autowired
    private ISysConfigService configService;
    
    @Autowired
    private ISysUserService sysUserService;
    
    @Autowired
    private ISysRoleService sysRoleService;

    @Override
    public String sendAllocationTableNotifications(List<ExpenseAllocationTable> allocationTables) {
        if (allocationTables == null || allocationTables.isEmpty()) {
            log.warn("分摊表列表为空，无需发送通知");
            return "分摊表列表为空，无需发送通知";
        }

        List<String> successResults = new ArrayList<>();
        List<String> failResults = new ArrayList<>();

        for (ExpenseAllocationTable allocationTable : allocationTables) {
            try {
                String result = sendSingleAllocationTableNotification(allocationTable);
                if (result.contains("发送成功")) {
                    successResults.add(result);
                } else {
                    failResults.add(result);
                }
            } catch (Exception e) {
                String errorMsg = String.format("分摊表[%s]通知发送失败：%s", 
                    allocationTable.getTableName(), e.getMessage());
                failResults.add(errorMsg);
                log.error(errorMsg, e);
            }
        }

        // 构建汇总结果
        StringBuilder resultBuilder = new StringBuilder();
        if (!successResults.isEmpty()) {
            resultBuilder.append("成功发送 ").append(successResults.size()).append(" 个通知");
        }
        if (!failResults.isEmpty()) {
            if (resultBuilder.length() > 0) {
                resultBuilder.append("，");
            }
            resultBuilder.append("失败 ").append(failResults.size()).append(" 个通知");
        }

        String finalResult = resultBuilder.toString();
        log.info("批量分摊表通知发送完成：{}", finalResult);
        return finalResult;
    }

    @Override
    public String sendSingleAllocationTableNotification(ExpenseAllocationTable allocationTable) {
        if (allocationTable == null) {
            log.warn("分摊表信息为空，无法发送通知");
            return "分摊表信息为空，无法发送通知";
        }

        try {
            log.info("开始发送分摊表核对通知，分摊表：{}", allocationTable.getTableName());

            // 获取系统配置
            String sendCharSmsUrl = configService.selectConfigByKey("expense.sendCharSms.url");
            String sysCode = configService.selectConfigByKey("expense.sendCharSms.sysCode");
            String sysName = configService.selectConfigByKey("expense.sendCharSms.sysName");

            if (StringUtils.isEmpty(sendCharSmsUrl) || StringUtils.isEmpty(sysCode) || StringUtils.isEmpty(sysName)) {
                String errorMsg = "系统配置信息不完整，请检查通知相关配置";
                log.error(errorMsg);
                return errorMsg;
            }

            // 获取分摊表核对人员的EHR号列表
            List<String> checkerEhrs = getAllCheckerEhrsByRole("expense_allocationTable_checker");
            if (checkerEhrs == null || checkerEhrs.isEmpty()) {
                String errorMsg = "未找到分摊表核对人员信息";
                log.warn(errorMsg);
                return errorMsg;
            }

            // 构建通知消息
            String title = "[分摊表核对通知]";
            String content = String.format("您有一份分摊表需要核对，请及时处理。" +
                    "分摊表信息：%s，费用类型：%s，计费周期：%s，制表人：%s", 
                    allocationTable.getTableName(),
                    allocationTable.getExpenseType(),
                    allocationTable.getBillingCycle(),
                    allocationTable.getPreparer());

            // 发送通知给所有核对人员
            List<String> successResults = new ArrayList<>();
            List<String> failResults = new ArrayList<>();

            for (String ehrNumber : checkerEhrs) {
                try {
                    Expense_wechat weChat = new Expense_wechat();
                    weChat.setMsgTitle(title);
                    weChat.setMsgContent(content);
                    weChat.setRecvEhr(ehrNumber);
                    weChat.setSysCode(sysCode);
                    weChat.setSysName(sysName);

                    // 发送HTTP请求
                    RestTemplate restTemplate = new RestTemplate();
                    HttpHeaders headers = new HttpHeaders();
                    headers.setContentType(MediaType.APPLICATION_JSON);
                    HttpEntity<Expense_wechat> entity = new HttpEntity<>(weChat, headers);

                    ResponseEntity<String> response = restTemplate.postForEntity(sendCharSmsUrl, entity, String.class);
                    
                    if (response.getStatusCode().is2xxSuccessful()) {
                        String successMsg = String.format("分摊表[%s]通知发送成功，接收人EHR：%s", 
                            allocationTable.getTableName(), ehrNumber);
                        successResults.add(successMsg);
                        log.info(successMsg);
                    } else {
                        String failMsg = String.format("分摊表[%s]通知发送失败，接收人EHR：%s，HTTP状态：%s", 
                            allocationTable.getTableName(), ehrNumber, response.getStatusCode());
                        failResults.add(failMsg);
                        log.error(failMsg);
                    }
                } catch (Exception e) {
                    String failMsg = String.format("分摊表[%s]通知发送异常，接收人EHR：%s，错误：%s", 
                        allocationTable.getTableName(), ehrNumber, e.getMessage());
                    failResults.add(failMsg);
                    log.error(failMsg, e);
                }
            }

            // 构建结果消息
            StringBuilder resultBuilder = new StringBuilder();
            if (!successResults.isEmpty()) {
                resultBuilder.append("发送成功 ").append(successResults.size()).append(" 个通知");
            }
            if (!failResults.isEmpty()) {
                if (resultBuilder.length() > 0) {
                    resultBuilder.append("，");
                }
                resultBuilder.append("发送失败 ").append(failResults.size()).append(" 个通知");
            }

            return resultBuilder.toString();

        } catch (Exception e) {
            String errorMsg = String.format("分摊表[%s]通知发送时发生异常：%s", 
                allocationTable.getTableName(), e.getMessage());
            log.error(errorMsg, e);
            return errorMsg;
        }
    }

    @Override
    public List<String> getAllCheckerEhrsByRole(String roleKey) {
        List<String> ehrNumbers = new ArrayList<>();

        try {
            // 首先根据角色标识查询角色ID
            List<com.ruoyi.common.core.domain.entity.SysRole> roles = sysRoleService.selectRoleAll();
            Long roleId = null;
            for (com.ruoyi.common.core.domain.entity.SysRole role : roles) {
                if (roleKey.equals(role.getRoleKey())) {
                    roleId = role.getRoleId();
                    break;
                }
            }

            if (roleId == null) {
                log.warn("未找到角色标识[{}]对应的角色", roleKey);
                return ehrNumbers;
            }

            // 根据角色ID查询所有具有该角色的用户
            com.ruoyi.common.core.domain.entity.SysUser queryUser = new com.ruoyi.common.core.domain.entity.SysUser();
            queryUser.setRoleId(roleId);
            List<com.ruoyi.common.core.domain.entity.SysUser> users = sysUserService.selectAllocatedList(queryUser);

            if (users != null && !users.isEmpty()) {
                for (com.ruoyi.common.core.domain.entity.SysUser user : users) {
                    String ehrNumber = getUserEhrByUserName(user.getUserName());
                    if (StringUtils.isNotEmpty(ehrNumber)) {
                        ehrNumbers.add(ehrNumber);
                        log.info("找到分摊表核对人员：{}，EHR号：{}", user.getUserName(), ehrNumber);
                    } else {
                        log.warn("用户[{}]未找到对应的EHR号", user.getUserName());
                    }
                }
            } else {
                log.warn("未找到具有角色[{}]的用户", roleKey);
            }

        } catch (Exception e) {
            log.error("查询角色[{}]的用户时发生异常", roleKey, e);
        }

        return ehrNumbers;
    }

    @Override
    public String getUserEhrByUserName(String userName) {
        if (StringUtils.isEmpty(userName)) {
            return null;
        }

        try {
            // 查询sys_user表中user_name字段匹配的用户，取其login_name字段作为EHR号
            log.info("查询用户[{}]的EHR号", userName);

            com.ruoyi.common.core.domain.entity.SysUser queryUser = new com.ruoyi.common.core.domain.entity.SysUser();
            queryUser.setUserName(userName);
            List<com.ruoyi.common.core.domain.entity.SysUser> userList = sysUserService.selectUserList(queryUser);
            if (userList != null && !userList.isEmpty()) {
                // 手动在返回的用户列表中找到匹配userName的用户
                for (com.ruoyi.common.core.domain.entity.SysUser sysUser : userList) {
                    if (userName.equals(sysUser.getUserName())) {
                        log.info("找到用户[{}]，EHR号为[{}]", userName, sysUser.getLoginName());
                        return sysUser.getLoginName();
                    }
                }
                log.warn("在用户列表中未找到用户名为[{}]的用户", userName);
            } else {
                log.warn("用户列表为空，未找到任何用户信息");
            }

            log.warn("未找到用户名为[{}]的用户信息", userName);
            return null;

        } catch (Exception e) {
            log.error("查询用户[{}]EHR号时发生异常", userName, e);
            return null;
        }
    }
}
