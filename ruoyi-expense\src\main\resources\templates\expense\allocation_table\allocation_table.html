<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('分摊表管理')" />
</head>
<body class="gray-bg">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <!-- 添加折叠控制按钮 -->
                    <div class="search-collapse-header" style="margin-bottom: 10px;">
                        <a class="btn btn-default btn-sm" onclick="toggleSearchForm()" id="searchToggleBtn">
                            <i class="fa fa-chevron-down" id="searchToggleIcon"></i> 展开筛选条件
                        </a>
                    </div>
                    <!-- 默认隐藏的筛选表单 -->
                    <div class="select-list" id="searchForm" style="display: none;">
                        <ul>
                            <li>
                                <label>分摊表名称：</label>
                                <input type="text" name="tableName" placeholder="请输入分摊表名称"/>
                            </li>
                            <li>
                                <label>计费周期：</label>
                                <input type="text" name="billingCycle" placeholder="请输入计费周期"/>
                            </li>
                            <li>
                                <label>费用类型：</label>
                                <select name="expenseType" id="expenseType">
                                    <option value="">请选择费用类型</option>
                                </select>
                            </li>
                            <li>
                                <label>状态：</label>
                                <select name="status" id="status">
                                    <option value="">请选择状态</option>
                                    <option value="审核中">审核中</option>
                                    <option value="已审核">已审核</option>
                                    <option value="已拒绝">已拒绝</option>
                                </select>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>
            
            <div class="btn-group" id="toolbar" role="group">
                <!-- 按照要求的顺序：一键确认、一键导出、导出选中 -->
                <a class="btn btn-success btn-lg" onclick="confirmAllPending()" shiro:hasAnyRoles="admin,expense_allocationTable_checker" style="margin-right: 10px;">
                    <i class="fa fa-check-circle"></i> 一键确认
                </a>
                <a class="btn btn-primary btn-lg" onclick="exportAllPendingAllocationTables()" style="margin-right: 10px;">
                    <i class="fa fa-download"></i> 一键导出
                </a>
                <a class="btn btn-info btn-lg" onclick="exportSelectedAllocationTables()" style="margin-right: 10px;">
                    <i class="fa fa-download"></i> 导出选中
                </a>
                <a class="btn btn-danger btn-lg" onclick="$.operate.removeAll()" shiro:hasAnyRoles="admin,expense_admin" style="margin-right: 10px;">
                    <i class="fa fa-remove"></i> 删除
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>

    <!-- 确认分摊表对话框 -->
    <div class="modal fade" id="confirmModal" tabindex="-1" role="dialog" aria-labelledby="confirmModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="confirmModalLabel">确认分摊表</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="confirmForm">
                        <input type="hidden" id="confirmId" name="id">
                        <div class="form-group">
                            <label for="responsiblePerson">负责人 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="responsiblePerson" name="responsiblePerson" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="submitConfirm()">确认</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 退回分摊表对话框 -->
    <div class="modal fade" id="rejectModal" tabindex="-1" role="dialog" aria-labelledby="rejectModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="rejectModalLabel">退回分摊表</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="rejectForm">
                        <input type="hidden" id="rejectId" name="id">
                        <div class="form-group">
                            <label for="rejectComments">退回原因 <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="rejectComments" name="comments" rows="4" placeholder="请输入退回原因" required></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" onclick="submitReject()">退回</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 导出分摊表对话框 -->
    <div class="modal fade" id="exportShareModal" tabindex="-1" role="dialog" aria-labelledby="exportShareModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="exportShareModalLabel">导出分摊表</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="exportShareForm">
                        <div class="form-group">
                            <label for="exportExpenseType">费用类型 <span class="text-danger">*</span></label>
                            <select name="expenseType" id="exportExpenseType" class="form-control" required>
                                <option value="">请选择费用类型</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="exportBillingCycle">计费周期 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="exportBillingCycle" name="billingCycle" placeholder="请输入计费周期，如：202501 或 202501-06" required>
                            <small class="form-text text-muted">格式：YYYYMM（如：202501）或 YYYYMM-MM（如：202501-06）</small>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="submitExportShare()">导出</button>
                </div>
            </div>
        </div>
    </div>

    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        /*<![CDATA[*/
        var prefix = ctx + "expense/allocation_table";
        var isAdmin = /*[[${isAdmin}]]*/ false;
        var isChecker = /*[[${isChecker}]]*/ false;
        var currentUser = /*[[${currentUser}]]*/ '';
        /*]]>*/

        $(function() {
            var options = {
                url: prefix + "/list",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "分摊表管理",
                uniqueId: "id", // 设置唯一ID字段，用于局部刷新
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: 'ID',
                    visible: false
                },
                {
                    field: 'tableName',
                    title: '分摊表名称',
                    sortable: true
                },
                {
                    field: 'billingCycle',
                    title: '计费周期',
                    sortable: true
                },
                {
                    field: 'expenseType',
                    title: '费用类型',
                    sortable: true
                },
                {
                    field: 'preparer',
                    title: '制表人'
                },
                {
                    field: 'reviewer',
                    title: '复核人'
                },
                {
                    field: 'responsiblePerson',
                    title: '负责人'
                },
                {
                    field: 'status',
                    title: '状态',
                    align: 'center',
                    formatter: function(value, row, index) {
                        if (value == '审核中') {
                            return '<span class="badge badge-warning">审核中</span>';
                        } else if (value == '已审核') {
                            return '<span class="badge badge-success">已审核</span>';
                        } else if (value == '已拒绝') {
                            return '<span class="badge badge-danger">已拒绝</span>';
                        }
                        return value;
                    }
                },
                {
                    field: 'comments',
                    title: '修改意见',
                    formatter: function(value, row, index) {
                        if (value && value.length > 20) {
                            return '<span title="' + value + '">' + value.substring(0, 20) + '...</span>';
                        }
                        return value || '';
                    }
                },
                {
                    field: 'createTime',
                    title: '创建时间',
                    sortable: true
                },
                {
                    field: 'confirmTime',
                    title: '确认时间',
                    sortable: true,
                    formatter: function(value, row, index) {
                        return value ? value : '未确认';
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        
                        // 分摊表核对人可以确认和退回审核中的分摊表
                        if (isChecker && row.status == '审核中') {
                            actions.push('<a class="btn btn-success btn-xs" href="#" onclick="confirmAllocationTable(' + row.id + ')"><i class="fa fa-check"></i> 确认</a> ');
                            actions.push('<a class="btn btn-danger btn-xs" href="#" onclick="rejectAllocationTable(' + row.id + ')"><i class="fa fa-times"></i> 退回</a> ');
                        }
                        
                        // 管理员和分摊表核对人可以在线查看和导出
                        if (isAdmin || isChecker) {
                            actions.push('<a class="btn btn-info btn-xs" href="#" onclick="viewAllocationTable(' + row.id + ')"><i class="fa fa-eye"></i> 在线查看</a> ');
                            actions.push('<a class="btn btn-primary btn-xs" href="#" onclick="exportAllocationTable(' + row.id + ')"><i class="fa fa-download"></i> 导出</a> ');
                        }
                        
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
            
            // 加载费用类型下拉选项
            loadExpenseTypes();
        });

        // 切换筛选表单的显示/隐藏
        function toggleSearchForm() {
            var $searchForm = $("#searchForm");
            var $toggleBtn = $("#searchToggleBtn");
            var $toggleIcon = $("#searchToggleIcon");

            if ($searchForm.is(":visible")) {
                // 当前显示，需要隐藏
                $searchForm.slideUp(300);
                $toggleBtn.html('<i class="fa fa-chevron-down" id="searchToggleIcon"></i> 展开筛选条件');
            } else {
                // 当前隐藏，需要显示
                $searchForm.slideDown(300);
                $toggleBtn.html('<i class="fa fa-chevron-up" id="searchToggleIcon"></i> 收起筛选条件');
            }
        }

        // 加载费用类型
        function loadExpenseTypes() {
            $.post(ctx + "expense/bill_manage/getDictData", { dictType: "expense_type" }, function(result) {
                if (result.code == 0 && result.data) {
                    var $select = $("#expenseType");
                    $select.empty().append('<option value="">请选择费用类型</option>');
                    $.each(result.data, function(i, item) {
                        $select.append('<option value="' + item.dictValue + '">' + item.dictLabel + '</option>');
                    });
                }
            });
        }

        // 确认分摊表
        function confirmAllocationTable(id) {
            // 直接确认，不弹出对话框
            $.post(prefix + "/confirm", {
                id: id
            }, function(result) {
                if (result.code == 0) {
                    $.modal.alertSuccess("分摊表确认成功");
                    // 局部刷新表格数据，只刷新当前行
                    refreshTableRow(id);
                } else {
                    $.modal.alertError(result.msg || "确认失败");
                }
            });
        }

        // 提交确认（保留函数以防其他地方调用）
        function submitConfirm() {
            // 已改为直接确认，此函数保留为空
        }

        // 退回分摊表
        function rejectAllocationTable(id) {
            $("#rejectId").val(id);
            $("#rejectComments").val('');
            $("#rejectModal").modal('show');
        }

        // 提交退回
        function submitReject() {
            var id = $("#rejectId").val();
            var comments = $("#rejectComments").val();
            
            if (!comments) {
                $.modal.alertWarning("请输入退回原因");
                return;
            }
            
            // 直接退回，不需要二次确认
            $.post(prefix + "/reject", {
                id: id,
                comments: comments
            }, function(result) {
                if (result.code == 0) {
                    $.modal.alertSuccess("分摊表退回成功");
                    // 局部刷新表格数据，只刷新当前行
                    refreshTableRow(id);
                    $("#rejectModal").modal('hide');
                } else {
                    $.modal.alertError(result.msg || "退回失败");
                }
            });
        }

        // 在线查看分摊表
        function viewAllocationTable(id) {
            var url = prefix + "/viewDetail/" + id;
            $.modal.open("在线查看分摊表", url, 1300, 700);
        }

        // 导出分摊表
        function exportAllocationTable(id) {
            // 直接导出，不需要二次确认
            window.location.href = prefix + "/exportDetail/" + id;
        }


        // 提交导出分摊表
        function submitExportShare() {
            var expenseType = $("#exportExpenseType").val();
            var billingCycle = $("#exportBillingCycle").val();
            
            if (!expenseType) {
                $.modal.alertWarning("请选择费用类型");
                return;
            }
            
            if (!billingCycle) {
                $.modal.alertWarning("请输入计费周期");
                return;
            }
            
            // 验证计费周期格式
            var singleMonthPattern = /^\d{6}$/;
            var rangeMonthPattern = /^\d{6}-\d{2}$/;
            if (!singleMonthPattern.test(billingCycle) && !rangeMonthPattern.test(billingCycle)) {
                $.modal.alertWarning("计费周期格式不正确，请输入YYYYMM格式（如：202501）或YYYYMM-MM格式（如：202501-06）");
                return;
            }
            
            $.modal.confirm("是否要导出该分摊表？", function() {
                // 构造导出URL
                var url = ctx + "expense/allocation_table/exportShareByParams?expenseType=" + encodeURIComponent(expenseType) + "&billingCycle=" + encodeURIComponent(billingCycle);
                
                // 在新窗口中打开导出链接
                window.open(url, "_blank");
                
                // 关闭弹窗
                $("#exportShareModal").modal('hide');
            });
        }

        // 加载导出分摊表的费用类型下拉选项
        function loadExportExpenseTypes() {
            $.post(ctx + "expense/bill_manage/getDictData", { dictType: "expense_type" }, function(result) {
                if (result.code == 0 && result.data) {
                    var $select = $("#exportExpenseType");
                    $select.empty().append('<option value="">请选择费用类型</option>');
                    $.each(result.data, function(i, item) {
                        $select.append('<option value="' + item.dictValue + '">' + item.dictLabel + '</option>');
                    });
                }
            });
        }

        // 局部刷新表格行数据
        function refreshTableRow(id) {
            // 获取当前表格实例
            var $table = $("#bootstrap-table");
            
            // 重新获取该行的最新数据
            $.post(prefix + "/getRowData", { id: id }, function(result) {
                if (result.code == 0 && result.data) {
                    // 使用Bootstrap Table的API更新指定行的数据
                    $table.bootstrapTable('updateByUniqueId', {
                        id: id,
                        row: result.data
                    });
                    
                    // 延迟一下再刷新，确保数据更新完成
                    setTimeout(function() {
                        // 重新渲染该行，确保操作按钮正确显示
                        $table.bootstrapTable('refreshRow', {
                            index: $table.bootstrapTable('getRowByUniqueId', id).index
                        });
                    }, 100);
                }
            });
        }

        // 导出选中的分摊表
        function exportSelectedAllocationTables() {
            var selectedIds = $.table.selectColumns('id');
            if (selectedIds.length == 0) {
                $.modal.alertWarning("请选择要导出的分摊表");
                return;
            }

            $.modal.confirm("确认要导出选中的 " + selectedIds.length + " 个分摊表吗？", function() {
                $.modal.loading("正在准备导出，请稍候...");

                // 使用延时机制逐个导出，避免浏览器弹窗阻止
                var exportIndex = 0;
                var exportInterval = setInterval(function() {
                    if (exportIndex < selectedIds.length) {
                        var id = selectedIds[exportIndex];
                        // 使用现有的exportAllocationTable逻辑，在新窗口中打开
                        window.open(prefix + "/exportDetail/" + id, "_blank");
                        exportIndex++;
                    } else {
                        // 所有导出完成
                        clearInterval(exportInterval);
                        $.modal.closeLoading();
                        $.modal.alertSuccess("已完成 " + selectedIds.length + " 个分摊表的导出，请检查浏览器下载");
                    }
                }, 500); // 每500毫秒导出一个，避免浏览器阻止
            });
        }

        // 一键导出所有审核中的分摊表
        function exportAllPendingAllocationTables() {
            $.modal.confirm("是否要导出所有处于'审核中'状态的分摊表吗？", function() {
                $.modal.loading("正在获取审核中的分摊表，请稍候...");

                // 先获取所有审核中状态的分摊表ID
                $.post(prefix + "/getPendingIds", function(result) {
                    if (result.code == 0 && result.data && result.data.length > 0) {
                        var pendingIds = result.data;
                        $.modal.loading("正在准备导出 " + pendingIds.length + " 个分摊表，请稍候...");

                        // 使用延时机制逐个导出，避免浏览器弹窗阻止
                        var exportIndex = 0;
                        var exportInterval = setInterval(function() {
                            if (exportIndex < pendingIds.length) {
                                var id = pendingIds[exportIndex];
                                // 使用现有的exportAllocationTable逻辑，在新窗口中打开
                                window.open(prefix + "/exportDetail/" + id, "_blank");
                                exportIndex++;
                            } else {
                                // 所有导出完成
                                clearInterval(exportInterval);
                                $.modal.closeLoading();
                                $.modal.alertSuccess("已完成 " + pendingIds.length + " 个审核中分摊表的导出，请检查浏览器下载");
                            }
                        }, 500); // 每500毫秒导出一个，避免浏览器阻止
                    } else {
                        $.modal.closeLoading();
                        $.modal.alertWarning("没有找到处于'审核中'状态的分摊表");
                    }
                }).fail(function() {
                    $.modal.closeLoading();
                    $.modal.alertError("网络错误，请稍后重试");
                });
            });
        }

        // 一键确认所有审核中的分摊表
        function confirmAllPending() {
            $.modal.confirm("是否要一键确认所有处于'审核中'状态的分摊表？", function() {
                $.modal.loading("正在处理，请稍候...");
                
                $.post(prefix + "/confirmAllPending", function(result) {
                    $.modal.closeLoading();
                    if (result.code == 0) {
                        $.modal.alertSuccess("成功确认 " + result.data + " 个分摊表");
                        // 刷新整个表格
                        $("#bootstrap-table").bootstrapTable('refresh');
                    } else {
                        $.modal.alertError(result.msg || "一键确认失败");
                    }
                }).fail(function() {
                    $.modal.closeLoading();
                    $.modal.alertError("网络错误，请稍后重试");
                });
            });
        }
    </script>
</body>
</html> 